import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import { Search as SearchIcon, Filter, Grid, List, ArrowLeft, Home, X, ChevronRight } from 'lucide-react';
import ProductCard from '../components/ProductCard';
import { searchProducts as apiSearchProducts } from '../lib/api';
import { Product } from '../types';
import { useCart } from '../context/CartContext';

const SearchPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const query = new URLSearchParams(location.search).get('q') || '';
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortOption, setSortOption] = useState<string>('default');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState(query);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [priceFilter, setPriceFilter] = useState<string | null>(null);
  const [availabilityFilter, setAvailabilityFilter] = useState<string | null>(null);
  const [filteredResults, setFilteredResults] = useState<Product[]>([]);
  const { addToCart } = useCart();

  const performSearch = async (searchTerm: string) => {
    if (!searchTerm) return;

    try {
      setLoading(true);
      let results = await apiSearchProducts(searchTerm);

      // Apply sorting
      switch (sortOption) {
        case 'price-low':
          results = [...results].sort((a, b) => a.price - b.price);
          break;
        case 'price-high':
          results = [...results].sort((a, b) => b.price - a.price);
          break;
        case 'name-asc':
          results = [...results].sort((a, b) => a.name.localeCompare(b.name));
          break;
        default:
          // Default sort or no sort
          break;
      }

      setSearchResults(results);
      setError(null);
    } catch (err) {
      console.error('Error searching products:', err);
      setError('Failed to search products. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    performSearch(query);
  }, [query, sortOption]);

  // Apply filters whenever searchResults, priceFilter, or availabilityFilter changes
  useEffect(() => {
    let filtered = [...searchResults];

    // Apply price filter
    if (priceFilter) {
      switch (priceFilter) {
        case 'under-100':
          filtered = filtered.filter(product => product.price < 100);
          break;
        case '100-200':
          filtered = filtered.filter(product => product.price >= 100 && product.price <= 200);
          break;
        case 'above-200':
          filtered = filtered.filter(product => product.price > 200);
          break;
      }
    }

    // Apply availability filter
    if (availabilityFilter) {
      switch (availabilityFilter) {
        case 'in-stock':
          filtered = filtered.filter(product =>
            product.is_available && (product.stock_quantity > 0 || product.stock > 0)
          );
          break;
        case 'out-of-stock':
          filtered = filtered.filter(product =>
            !product.is_available || product.stock_quantity <= 0 || product.stock <= 0
          );
          break;
      }
    }

    setFilteredResults(filtered);
  }, [searchResults, priceFilter, availabilityFilter]);

  const handlePriceFilter = (filter: string) => {
    setPriceFilter(priceFilter === filter ? null : filter);
  };

  const handleAvailabilityFilter = (filter: string) => {
    setAvailabilityFilter(availabilityFilter === filter ? null : filter);
  };

  const clearFilters = () => {
    setPriceFilter(null);
    setAvailabilityFilter(null);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Update the URL with the new search query
    window.history.pushState({}, '', `/search?q=${encodeURIComponent(searchQuery)}`);
    // Trigger a new search
    performSearch(searchQuery);
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setFilteredResults([]);
    navigate('/');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen pt-16">
      <div className="bg-primary-50 py-4 md:py-8">
        <div className="container mx-auto px-4">
          {/* Desktop Navigation Breadcrumb - Hidden on Mobile */}
          <nav className="hidden md:flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <Link to="/" className="hover:text-primary-600 flex items-center">
              <Home className="w-4 h-4 mr-1" />
              Home
            </Link>
            <ChevronRight className="w-4 h-4" />
            <span className="text-gray-800">Search Results</span>
          </nav>

          {/* Mobile-Optimized Header */}
          <div className="md:hidden">
            {/* Mobile Header Row */}
            <div className="flex items-center justify-between mb-3">
              <button
                onClick={handleGoBack}
                className="flex items-center space-x-2 text-gray-700 hover:text-primary-600 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span className="text-sm">Back</span>
              </button>

              <button
                onClick={handleClearSearch}
                className="flex items-center space-x-1 text-gray-700 hover:text-red-600 transition-colors"
              >
                <X className="w-4 h-4" />
                <span className="text-sm">Clear</span>
              </button>
            </div>

            {/* Mobile Search Results Title */}
            <div className="mb-3">
              <h1 className="text-xl font-bold text-gray-800">Search Results</h1>
              <p className="text-sm text-gray-600">"{query}"</p>
            </div>
          </div>

          {/* Desktop Header - Hidden on Mobile */}
          <div className="hidden md:block">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleGoBack}
                  className="flex items-center space-x-2 text-gray-700 hover:text-primary-600 transition-colors"
                >
                  <ArrowLeft className="w-5 h-5" />
                  <span>Back</span>
                </button>
                <div>
                  <h1 className="text-3xl font-bold text-gray-800">Search Results</h1>
                  <p className="text-gray-600 mt-1">
                    Showing results for "{query}"
                  </p>
                </div>
              </div>

              <button
                onClick={handleClearSearch}
                className="flex items-center space-x-2 text-gray-700 hover:text-red-600 transition-colors"
              >
                <X className="w-5 h-5" />
                <span>Clear Search</span>
              </button>
            </div>
          </div>

          {/* Enhanced Search Form - Responsive */}
          <form onSubmit={handleSearch} className="relative w-full md:max-w-lg">
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 md:w-5 md:h-5" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search for products..."
                className="w-full pl-10 md:pl-12 pr-16 md:pr-20 py-2 md:py-3 text-sm md:text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all"
              />
              {searchQuery && (
                <button
                  type="button"
                  onClick={() => setSearchQuery('')}
                  className="absolute right-12 md:right-14 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X className="w-3 h-3 md:w-4 md:h-4" />
                </button>
              )}
              <button
                type="submit"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 px-2 md:px-3 py-1 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors text-xs md:text-sm"
              >
                Search
              </button>
            </div>
          </form>

          {/* Quick Navigation - Desktop Only */}
          <div className="hidden md:block mt-4">
            <Link
              to="/"
              className="text-sm text-primary-600 hover:text-primary-700 bg-white px-3 py-1 rounded-full border border-primary-200 hover:border-primary-300 transition-colors"
            >
              Back to Home
            </Link>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Mobile-First Responsive Controls Section */}
        <div className="mb-8">
          {/* Results Count */}
          <div className="mb-4">
            <span className="text-gray-700 text-sm md:text-base">Found {searchResults.length} results</span>
          </div>

          {/* Mobile Layout - Stacked Controls */}
          <div className="block md:hidden space-y-3">
            {/* Row 1: Filter Button */}
            <div className="flex justify-center">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center justify-center space-x-2 w-full py-3 px-4 bg-white border border-gray-300 rounded-lg text-gray-700 hover:text-primary-600 hover:border-primary-300 transition-colors"
              >
                <Filter className="w-5 h-5" />
                <span className="font-medium">Filter & Sort</span>
              </button>
            </div>

            {/* Row 2: Sort Dropdown */}
            <div>
              <select
                value={sortOption}
                onChange={(e) => setSortOption(e.target.value)}
                className="w-full py-3 px-4 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white"
              >
                <option value="default">Sort by: Default</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="name-asc">Name: A to Z</option>
              </select>
            </div>

            {/* Row 3: View Mode Toggles */}
            <div className="flex items-center justify-center space-x-3">
              <span className="text-sm text-gray-600 mr-2">View:</span>
              <button
                onClick={() => setViewMode('grid')}
                className={`flex items-center justify-center p-3 rounded-lg transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-primary-100 text-primary-700 border-2 border-primary-300'
                    : 'bg-gray-100 text-gray-600 border-2 border-gray-200 hover:bg-gray-200'
                }`}
              >
                <Grid className="w-5 h-5" />
                <span className="ml-2 text-sm font-medium">Grid</span>
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`flex items-center justify-center p-3 rounded-lg transition-colors ${
                  viewMode === 'list'
                    ? 'bg-primary-100 text-primary-700 border-2 border-primary-300'
                    : 'bg-gray-100 text-gray-600 border-2 border-gray-200 hover:bg-gray-200'
                }`}
              >
                <List className="w-5 h-5" />
                <span className="ml-2 text-sm font-medium">List</span>
              </button>
            </div>
          </div>

          {/* Desktop Layout - Horizontal Controls */}
          <div className="hidden md:flex justify-between items-center">
            <div className="flex items-center space-x-6">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2 text-gray-700 hover:text-primary-600 transition-colors"
              >
                <Filter className="w-5 h-5" />
                <span>Filter & Sort</span>
              </button>

              <div>
                <select
                  value={sortOption}
                  onChange={(e) => setSortOption(e.target.value)}
                  className="py-2 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white"
                >
                  <option value="default">Sort by: Default</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="name-asc">Name: A to Z</option>
                </select>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 mr-2">View:</span>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-primary-100 text-primary-700'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <Grid className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'list'
                    ? 'bg-primary-100 text-primary-700'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {showFilters && (
          <div className="bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200">
            <h3 className="text-lg font-medium text-gray-800 mb-3">Filters</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">Price Range</p>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePriceFilter('under-100')}
                    className={`px-3 py-1 border rounded-md text-sm transition-colors ${
                      priceFilter === 'under-100'
                        ? 'bg-primary-500 text-white border-primary-500'
                        : 'border-gray-300 hover:bg-primary-50 hover:border-primary-300'
                    }`}
                  >
                    Under ₹100
                  </button>
                  <button
                    onClick={() => handlePriceFilter('100-200')}
                    className={`px-3 py-1 border rounded-md text-sm transition-colors ${
                      priceFilter === '100-200'
                        ? 'bg-primary-500 text-white border-primary-500'
                        : 'border-gray-300 hover:bg-primary-50 hover:border-primary-300'
                    }`}
                  >
                    ₹100 - ₹200
                  </button>
                  <button
                    onClick={() => handlePriceFilter('above-200')}
                    className={`px-3 py-1 border rounded-md text-sm transition-colors ${
                      priceFilter === 'above-200'
                        ? 'bg-primary-500 text-white border-primary-500'
                        : 'border-gray-300 hover:bg-primary-50 hover:border-primary-300'
                    }`}
                  >
                    Above ₹200
                  </button>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">Availability</p>
                <div className="space-x-4">
                  <label className="inline-flex items-center">
                    <input
                      type="checkbox"
                      checked={availabilityFilter === 'in-stock'}
                      onChange={() => handleAvailabilityFilter('in-stock')}
                      className="form-checkbox text-primary-600"
                    />
                    <span className="ml-2 text-sm text-gray-700">In Stock</span>
                  </label>
                  <label className="inline-flex items-center">
                    <input
                      type="checkbox"
                      checked={availabilityFilter === 'out-of-stock'}
                      onChange={() => handleAvailabilityFilter('out-of-stock')}
                      className="form-checkbox text-primary-600"
                    />
                    <span className="ml-2 text-sm text-gray-700">Out of Stock</span>
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <button
                  onClick={clearFilters}
                  className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
                >
                  Clear Filters
                </button>
                <button
                  onClick={() => setShowFilters(false)}
                  className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
                >
                  Apply Filters
                </button>
              </div>
            </div>
          </div>
        )}

        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
          </div>
        )}

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {!loading && !error && filteredResults.length === 0 && searchResults.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No products found matching "{query}"</p>
          </div>
        ) : !loading && !error && filteredResults.length === 0 && searchResults.length > 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No products match the selected filters</p>
            <button
              onClick={clearFilters}
              className="mt-4 inline-block text-primary-600 hover:text-primary-700"
            >
              Clear filters to see all results
            </button>
          </div>
        ) : !loading && !error && viewMode === 'grid' ? (
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
            {filteredResults.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        ) : !loading && !error ? (
          <div className="space-y-4">
            {filteredResults.map((product) => {
              // Check if product is out of stock (same logic as ProductCard)
              const isOutOfStock = !product.is_available ||
                (product.stock_quantity !== undefined && product.stock_quantity <= 0) ||
                (product.stock !== undefined && product.stock <= 0);

              // Get available stock
              const availableStock = product.is_available ?
                (product.stock_quantity !== undefined ? product.stock_quantity : product.stock || 0) : 0;

              return (
                <div key={product.id} className="flex border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow bg-white">
                  <div className="relative w-32 h-32 flex-shrink-0">
                    <img
                      src={product.image || product.image_url}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />

                    {/* Out of Stock Overlay */}
                    {isOutOfStock && (
                      <div className="absolute top-0 right-0 left-0 bg-red-500 text-white text-center py-1 text-xs">
                        Out of Stock
                      </div>
                    )}

                    {/* Low Stock Overlay */}
                    {!isOutOfStock && availableStock <= 10 && (
                      <div className="absolute top-0 right-0 left-0 bg-orange-500 text-white text-center py-1 text-xs">
                        Low Stock: Only {availableStock} left
                      </div>
                    )}
                  </div>

                  <div className="p-4 flex-1 flex flex-col">
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <h3 className="text-lg font-medium text-gray-800">{product.name}</h3>
                        <span className="text-xs text-gray-500">{product.unit || '1 unit'}</span>
                      </div>
                      <p className="text-gray-600 text-sm mt-1">{product.description}</p>

                      {/* Stock information for list view */}
                      {!isOutOfStock && availableStock <= 10 && (
                        <p className="text-orange-600 text-xs mt-2">Only {availableStock} left in stock</p>
                      )}
                    </div>

                    <div className="flex justify-between items-center mt-4">
                      <p className="text-primary-600 font-bold">₹{product.price}</p>

                      {/* Add to Cart Button with Out of Stock handling */}
                      {isOutOfStock ? (
                        <button
                          disabled
                          className="px-4 py-2 bg-gray-200 text-gray-500 rounded-md cursor-not-allowed"
                        >
                          Out of Stock
                        </button>
                      ) : (
                        <button
                          onClick={() => addToCart(product)}
                          className="px-4 py-2 bg-primary-100 hover:bg-primary-200 text-primary-700 rounded-md transition-colors"
                        >
                          Add to Cart
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default SearchPage;